import Foundation
import OSLog
import AppKit

actor ClipboardSender {
    // MARK: - Properties (matching Python sender.py)
    private weak var appViewModel: AppViewModel?
    private let ntfyService: NtfyService
    private let clipboardManager: ClipboardManager
    private let configurationManager: ConfigurationManager
    private let logManager = LogManager.shared

    // MARK: - State Management (matching Python implementation)
    private var isMonitoring = false
    private var monitoringTask: Task<Void, Never>?
    private var lastPostedText: String? = nil

    // MARK: - Configuration (matching Python config)
    private let pollInterval: TimeInterval = 1.0 // Check every 1 second (matching Python default)
    private let maxRetryAttempts = 3
    private let retryDelay: TimeInterval = 2.0

    // MARK: - Error Types
    enum CriticalError: Error {
        case invalidConfiguration
    }

    // MARK: - Initialization
    init(appViewModel: AppViewModel,
         ntfyService: NtfyService,
         clipboardManager: ClipboardManager,
         configurationManager: ConfigurationManager) {
        self.appViewModel = appViewModel
        self.ntfyService = ntfyService
        self.clipboardManager = clipboardManager
        self.configurationManager = configurationManager

        logManager.info("ClipboardSender", "ClipboardSender initialized with poll interval: \(self.pollInterval)s")
    }

    // MARK: - Public Methods (matching Python run method)
    func startMonitoring() async {
        guard !isMonitoring else {
            logManager.warning("ClipboardSender", "Already monitoring clipboard")
            return
        }

        guard configurationManager.enableSending else {
            logManager.info("ClipboardSender", "Clipboard Sender is disabled in configuration")
            return
        }

        guard configurationManager.isSendConfigurationValid() else {
            logManager.error("ClipboardSender", "Sender is enabled but configuration is invalid. Disabling sender.")
            return
        }

        isMonitoring = true
        logManager.info("ClipboardSender", "Starting clipboard monitoring loop (Sender)...")

        monitoringTask = Task {
            await monitorClipboard()
        }
    }

    func stopMonitoring() async {
        guard isMonitoring else { return }

        isMonitoring = false
        monitoringTask?.cancel()
        monitoringTask = nil

        logManager.info("ClipboardSender", "Stopped clipboard monitoring")
    }

    // MARK: - Private Methods (matching Python implementation)

    /// Main monitoring loop (matching Python run method)
    private func monitorClipboard() async {
        logManager.debug("ClipboardSender", "Starting clipboard monitoring loop")
        while isMonitoring && !Task.isCancelled {
            do {
                await checkAndSend()

                // Wait for the next check (matching Python poll_interval)
                try await Task.sleep(nanoseconds: UInt64(pollInterval * 1_000_000_000))

            } catch is CancellationError {
                logManager.info("ClipboardSender", "Sender monitoring cancelled")
                break
            } catch {
                logManager.error("ClipboardSender", "Error in clipboard monitoring loop: \(error.localizedDescription)")

                // Wait before retrying (matching Python error handling)
                let errorDelay = min(pollInterval * 2, 10.0)
                logManager.debug("ClipboardSender", "Waiting \(errorDelay)s before retrying after error")
                try? await Task.sleep(nanoseconds: UInt64(errorDelay * 1_000_000_000))
            }
        }
    }

    /// Check clipboard and send if necessary (matching Python check_and_send)
    private func checkAndSend() async {
        do {
            // Check if clipboard has changed (matching Python implementation)
            let hasChanged = await MainActor.run {
                clipboardManager.hasChanged()
            }

            guard hasChanged else {
                return
            }

            // Get current text content (matching Python get_text)
            guard let currentText = await MainActor.run(body: {
                clipboardManager.getText()
            }) else {
                // Update change count even if no text (matching Python)
                await MainActor.run {
                    clipboardManager.updateLastChangeCount()
                }
                return
            }

            // Update change count after reading (matching Python)
            await MainActor.run {
                clipboardManager.updateLastChangeCount()
            }

            // Check if this is the same as last posted text (matching Python)
            guard currentText != lastPostedText else {
                logManager.debug("ClipboardSender", "Current text matches last posted text, skipping")
                return
            }

            // Check loop prevention (matching Python shared_state check)
            let shouldSend = await appViewModel?.shouldSendContent(identifier: currentText.sha256) ?? false
            guard shouldSend else {
                logManager.info("ClipboardSender", "Clipboard content matches the last received content. Skipping send to prevent loop.")
                return
            }

            logManager.info("ClipboardSender", "Detected new clipboard text (\(currentText.count) chars), preparing to send...")

            // Send text as file (matching Python post_text_as_file)
            let success = await sendTextAsFile(currentText)

            if success {
                logManager.info("ClipboardSender", "Successfully sent new clipboard text to ntfy.")
                lastPostedText = currentText
                // Clear last received to prevent false loop detection (matching Python)
                await appViewModel?.clearLastReceivedContent()
            } else {
                logManager.warning("ClipboardSender", "Failed to send clipboard text to ntfy.")
            }

        } catch is CancellationError {
            logManager.info("ClipboardSender", "Sender check_and_send cancelled.")
            return // Exit gracefully
        } catch {
            logManager.error("ClipboardSender", "Error in clipboard check and send: \(error.localizedDescription)")

            // Sleep before retrying (matching Python error handling)
            try? await Task.sleep(nanoseconds: UInt64(min(pollInterval * 2, 10) * 1_000_000_000))
        }
    }

    /// Send text as file using ntfy service (matching Python implementation)
    private func sendTextAsFile(_ text: String) async -> Bool {
        guard let topicURL = configurationManager.getSendTopicURL() else {
            logManager.error("ClipboardSender", "Send topic URL not configured")
            return false
        }

        let authHeader = configurationManager.getSendAuthenticationHeader()
        logManager.debug("ClipboardSender", "Sending text to: \(topicURL.absoluteString)")

        var attempt = 0
        while attempt < maxRetryAttempts {
            do {
                try await ntfyService.sendTextAsFile(text, to: topicURL, authHeader: authHeader)

                logManager.info("ClipboardSender", "Successfully sent text as file (attempt \(attempt + 1))")
                await notifySuccessfulSync()
                return true

            } catch {
                attempt += 1
                logManager.warning("ClipboardSender", "Failed to send text as file (attempt \(attempt)): \(error.localizedDescription)")

                if attempt >= maxRetryAttempts {
                    logManager.error("ClipboardSender", "Max retry attempts reached for text sending")
                    await notifyError(error)
                    return false
                }

                // Exponential backoff (matching Python retry logic)
                let delay = retryDelay * Double(attempt)
                try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }

        return false
    }

    // MARK: - Notification Methods

    private func notifySuccessfulSync() async {
        if let viewModel = appViewModel {
            await MainActor.run {
                viewModel.lastSyncTime = Date()
            }
        }
    }

    private func notifyError(_ error: Error) async {
        if let viewModel = appViewModel {
            await MainActor.run {
                viewModel.handleError(error)
            }
        }
    }
}

// MARK: - String Extension for SHA256 (for content identification)

extension String {
    var sha256: String {
        guard let data = self.data(using: .utf8) else { return "" }
        return data.sha256
    }
}
