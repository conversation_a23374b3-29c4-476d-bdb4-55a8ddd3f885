import SwiftUI

// MARK: - Liquid Glass Effect Components
// Inspired by Apple's modern design language with glass-like materials and depth

enum LiquidGlassIntensity {
    case subtle, medium, strong
    
    var material: Material {
        switch self {
        case .subtle:
            return .ultraThinMaterial
        case .medium:
            return .thinMaterial
        case .strong:
            return .regularMaterial
        }
    }
    
    var opacity: Double {
        switch self {
        case .subtle:
            return 0.3
        case .medium:
            return 0.5
        case .strong:
            return 0.7
        }
    }
}

struct LiquidGlassEffect: View {
    let intensity: LiquidGlassIntensity
    
    var body: some View {
        ZStack {
            // Base glass material
            intensity.material
            
            // Subtle gradient overlay for depth
            LinearGradient(
                colors: [
                    Color.white.opacity(intensity.opacity * 0.3),
                    Color.clear,
                    Color.black.opacity(intensity.opacity * 0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Edge highlight for glass effect
            RoundedRectangle(cornerRadius: 0)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(intensity.opacity * 0.5),
                            Color.clear
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    ),
                    lineWidth: 1
                )
        }
    }
}

struct LiquidGlassCard<Content: View>: View {
    let content: Content
    let intensity: LiquidGlassIntensity
    let cornerRadius: CGFloat
    
    init(intensity: LiquidGlassIntensity = .medium, cornerRadius: CGFloat = 12, @ViewBuilder content: () -> Content) {
        self.intensity = intensity
        self.cornerRadius = cornerRadius
        self.content = content()
    }
    
    var body: some View {
        content
            .background(
                ZStack {
                    // Main glass background
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(intensity.material)
                    
                    // Gradient overlay for depth
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(intensity.opacity * 0.2),
                                    Color.clear,
                                    Color.black.opacity(intensity.opacity * 0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    // Border highlight
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(intensity.opacity * 0.4),
                                    Color.white.opacity(intensity.opacity * 0.1),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                }
                .shadow(
                    color: .black.opacity(0.1),
                    radius: intensity == .strong ? 8 : 4,
                    x: 0,
                    y: intensity == .strong ? 4 : 2
                )
            )
    }
}

struct LiquidGlassButton<Content: View>: View {
    let content: Content
    let action: () -> Void
    let intensity: LiquidGlassIntensity
    
    init(intensity: LiquidGlassIntensity = .medium, action: @escaping () -> Void, @ViewBuilder content: () -> Content) {
        self.intensity = intensity
        self.action = action
        self.content = content()
    }
    
    var body: some View {
        Button(action: action) {
            LiquidGlassCard(intensity: intensity, cornerRadius: 8) {
                content
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
            }
        }
        .buttonStyle(.plain)
    }
}

struct LiquidGlassBackground: View {
    let colors: [Color]
    let intensity: LiquidGlassIntensity
    
    init(colors: [Color] = [Color.blue.opacity(0.1), Color.purple.opacity(0.05), Color.clear], intensity: LiquidGlassIntensity = .subtle) {
        self.colors = colors
        self.intensity = intensity
    }
    
    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: colors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Glass material overlay
            intensity.material
        }
    }
}

// MARK: - Animated Liquid Glass Effects

struct AnimatedLiquidGlassCard<Content: View>: View {
    let content: Content
    let intensity: LiquidGlassIntensity
    let cornerRadius: CGFloat
    @State private var isHovered = false
    
    init(intensity: LiquidGlassIntensity = .medium, cornerRadius: CGFloat = 12, @ViewBuilder content: () -> Content) {
        self.intensity = intensity
        self.cornerRadius = cornerRadius
        self.content = content()
    }
    
    var body: some View {
        content
            .background(
                ZStack {
                    // Main glass background
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(intensity.material)
                    
                    // Animated gradient overlay
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(isHovered ? intensity.opacity * 0.4 : intensity.opacity * 0.2),
                                    Color.clear,
                                    Color.black.opacity(intensity.opacity * 0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .animation(.easeInOut(duration: 0.3), value: isHovered)
                    
                    // Border highlight
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(isHovered ? intensity.opacity * 0.6 : intensity.opacity * 0.4),
                                    Color.white.opacity(intensity.opacity * 0.1),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: isHovered ? 1.5 : 1
                        )
                        .animation(.easeInOut(duration: 0.3), value: isHovered)
                }
                .shadow(
                    color: .black.opacity(isHovered ? 0.15 : 0.1),
                    radius: isHovered ? (intensity == .strong ? 12 : 6) : (intensity == .strong ? 8 : 4),
                    x: 0,
                    y: isHovered ? (intensity == .strong ? 6 : 3) : (intensity == .strong ? 4 : 2)
                )
                .animation(.easeInOut(duration: 0.3), value: isHovered)
            )
            .onHover { hovering in
                isHovered = hovering
            }
    }
}
