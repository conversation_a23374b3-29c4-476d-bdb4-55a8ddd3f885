import SwiftUI

struct MainWindowView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var logManager = LogManager.shared
    @State private var selectedSidebarItem: SidebarItem = .status

    var body: some View {
        NavigationSplitView {
            // Sidebar
            SidebarView(selectedItem: $selectedSidebarItem)
                .navigationSplitViewColumnWidth(min: 220, ideal: 240, max: 280)
        } detail: {
            // Detail View
            DetailView(selectedItem: selectedSidebarItem)
                .navigationSplitViewColumnWidth(min: 500, ideal: 700, max: 1000)
        }
        .navigationTitle("NtfyClip")
        .frame(minWidth: 800, minHeight: 600)
        .background(
            LiquidGlassBackground(
                colors: [
                    Color.blue.opacity(0.1),
                    Color.purple.opacity(0.05),
                    Color.clear
                ],
                intensity: .subtle
            )
        )
    }
}

// MARK: - Sidebar Items
enum SidebarItem: String, CaseIterable, Identifiable {
    case status = "Status"
    case settings = "Settings"
    case logs = "Logs"

    var id: String { rawValue }

    var icon: String {
        switch self {
        case .status:
            return "chart.line.uptrend.xyaxis"
        case .settings:
            return "gearshape"
        case .logs:
            return "doc.text"
        }
    }
}

// MARK: - Sidebar View
struct SidebarView: View {
    @Binding var selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        VStack(spacing: 0) {
            // Header with app info
            VStack(spacing: 8) {
                Image(systemName: "doc.on.clipboard")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.blue.gradient)

                Text("NtfyClip")
                    .font(.headline)
                    .fontWeight(.semibold)

                Text("Clipboard Sync")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 20)
            .frame(maxWidth: .infinity)
            .background(
                LiquidGlassEffect(intensity: .medium)
            )

            // Navigation List
            List(SidebarItem.allCases, selection: $selectedItem) { item in
                SidebarItemView(item: item, isSelected: selectedItem == item)
                    .tag(item)
            }
            .listStyle(.sidebar)
            .scrollContentBackground(.hidden)

            // Footer with sync control
            VStack(spacing: 12) {
                Divider()

                Button(action: {
                    appViewModel.toggleSync()
                }) {
                    HStack {
                        Image(systemName: appViewModel.isSyncEnabled ? "stop.circle.fill" : "play.circle.fill")
                            .foregroundColor(appViewModel.isSyncEnabled ? .red : .green)

                        Text(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                }
                .buttonStyle(.bordered)
                .controlSize(.large)
                .help(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
            }
            .padding()
            .background(
                Color(NSColor.controlBackgroundColor)
                    .opacity(0.6)
                    .background(.regularMaterial)
            )
        }
        .navigationTitle("NtfyClip")
    }
}

// MARK: - Sidebar Item View
struct SidebarItemView: View {
    let item: SidebarItem
    let isSelected: Bool

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: item.icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(isSelected ? .white : .primary)
                .frame(width: 20)

            Text(item.rawValue)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isSelected ? .white : .primary)

            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(isSelected ? Color.accentColor : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(isSelected ? Color.clear : Color.secondary.opacity(0.2), lineWidth: 1)
                )
        )
        .contentShape(Rectangle())
    }
}

// MARK: - Detail View
struct DetailView: View {
    let selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        Group {
            switch selectedItem {
            case .status:
                StatusDetailView()
            case .settings:
                SettingsDetailView()
            case .logs:
                LogsDetailView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

// MARK: - Liquid Glass Components

enum LiquidGlassIntensity {
    case subtle, medium, strong

    var material: Material {
        switch self {
        case .subtle:
            return .ultraThinMaterial
        case .medium:
            return .thinMaterial
        case .strong:
            return .regularMaterial
        }
    }

    var opacity: Double {
        switch self {
        case .subtle:
            return 0.3
        case .medium:
            return 0.5
        case .strong:
            return 0.7
        }
    }
}

struct LiquidGlassBackground: View {
    let colors: [Color]
    let intensity: LiquidGlassIntensity

    init(colors: [Color] = [Color.blue.opacity(0.1), Color.purple.opacity(0.05), Color.clear], intensity: LiquidGlassIntensity = .subtle) {
        self.colors = colors
        self.intensity = intensity
    }

    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: colors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Glass material overlay
            Rectangle()
                .fill(intensity.material)
        }
    }
}

struct LiquidGlassEffect: View {
    let intensity: LiquidGlassIntensity

    var body: some View {
        ZStack {
            // Base glass material
            Rectangle()
                .fill(intensity.material)

            // Subtle gradient overlay for depth
            LinearGradient(
                colors: [
                    Color.white.opacity(intensity.opacity * 0.3),
                    Color.clear,
                    Color.black.opacity(intensity.opacity * 0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Edge highlight for glass effect
            RoundedRectangle(cornerRadius: 0)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(intensity.opacity * 0.5),
                            Color.clear
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    ),
                    lineWidth: 1
                )
        }
    }
}
