import SwiftUI

// MARK: - Liquid Glass Components

enum LiquidGlassIntensity {
    case subtle, medium, strong

    var material: Material {
        switch self {
        case .subtle:
            return .ultraThinMaterial
        case .medium:
            return .thinMaterial
        case .strong:
            return .regularMaterial
        }
    }

    var opacity: Double {
        switch self {
        case .subtle:
            return 0.3
        case .medium:
            return 0.5
        case .strong:
            return 0.7
        }
    }
}

enum LiquidGlassButtonStyle {
    case compact, standard, prominent

    var horizontalPadding: CGFloat {
        switch self {
        case .compact: return 12
        case .standard: return 16
        case .prominent: return 20
        }
    }

    var verticalPadding: CGFloat {
        switch self {
        case .compact: return 6
        case .standard: return 8
        case .prominent: return 12
        }
    }

    var cornerRadius: CGFloat {
        switch self {
        case .compact: return 6
        case .standard: return 8
        case .prominent: return 12
        }
    }

    var shadowRadius: CGFloat {
        switch self {
        case .compact: return 2
        case .standard: return 4
        case .prominent: return 8
        }
    }

    var shadowOffset: CGFloat {
        switch self {
        case .compact: return 1
        case .standard: return 2
        case .prominent: return 4
        }
    }
}

struct LiquidGlassEffect: View {
    let intensity: LiquidGlassIntensity

    var body: some View {
        ZStack {
            Rectangle()
                .fill(intensity.material)

            LinearGradient(
                colors: [
                    Color.white.opacity(intensity.opacity * 0.3),
                    Color.clear,
                    Color.black.opacity(intensity.opacity * 0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
}

struct LiquidGlassCard<Content: View>: View {
    let content: Content
    let intensity: LiquidGlassIntensity
    let cornerRadius: CGFloat

    init(intensity: LiquidGlassIntensity = .medium, cornerRadius: CGFloat = 12, @ViewBuilder content: () -> Content) {
        self.intensity = intensity
        self.cornerRadius = cornerRadius
        self.content = content()
    }

    var body: some View {
        content
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(intensity.material)

                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(intensity.opacity * 0.2),
                                    Color.clear,
                                    Color.black.opacity(intensity.opacity * 0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )

                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(intensity.opacity * 0.4),
                                    Color.white.opacity(intensity.opacity * 0.1),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                }
                .shadow(
                    color: .black.opacity(0.1),
                    radius: intensity == .strong ? 8 : 4,
                    x: 0,
                    y: intensity == .strong ? 4 : 2
                )
            )
    }
}

struct LiquidGlassButton<Content: View>: View {
    let content: Content
    let action: () -> Void
    let intensity: LiquidGlassIntensity
    let style: LiquidGlassButtonStyle

    init(intensity: LiquidGlassIntensity = .medium, style: LiquidGlassButtonStyle = .standard, action: @escaping () -> Void, @ViewBuilder content: () -> Content) {
        self.intensity = intensity
        self.style = style
        self.action = action
        self.content = content()
    }

    var body: some View {
        Button(action: action) {
            content
                .padding(.horizontal, style.horizontalPadding)
                .padding(.vertical, style.verticalPadding)
                .background(
                    RoundedRectangle(cornerRadius: style.cornerRadius)
                        .fill(intensity.material)
                        .overlay(
                            RoundedRectangle(cornerRadius: style.cornerRadius)
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(intensity.opacity * 0.6),
                                            Color.white.opacity(intensity.opacity * 0.2),
                                            Color.clear
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                        .shadow(
                            color: .black.opacity(0.1),
                            radius: style.shadowRadius,
                            x: 0,
                            y: style.shadowOffset
                        )
                )
        }
        .buttonStyle(.plain)
    }
}

struct LiquidGlassBackground: View {
    let colors: [Color]
    let intensity: LiquidGlassIntensity

    init(colors: [Color] = [Color.blue.opacity(0.1), Color.purple.opacity(0.05), Color.clear], intensity: LiquidGlassIntensity = .subtle) {
        self.colors = colors
        self.intensity = intensity
    }

    var body: some View {
        ZStack {
            LinearGradient(
                colors: colors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            Rectangle()
                .fill(intensity.material)
        }
    }
}

struct LiquidGlassToolbar<Content: View>: View {
    let content: Content
    let intensity: LiquidGlassIntensity

    init(intensity: LiquidGlassIntensity = .medium, @ViewBuilder content: () -> Content) {
        self.intensity = intensity
        self.content = content()
    }

    var body: some View {
        content
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                Rectangle()
                    .fill(intensity.material)
                    .overlay(
                        Rectangle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(intensity.opacity * 0.4),
                                        Color.clear,
                                        Color.black.opacity(intensity.opacity * 0.1)
                                    ],
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                    )
                    .shadow(
                        color: .black.opacity(0.1),
                        radius: 4,
                        x: 0,
                        y: 2
                    )
            )
    }
}

struct LiquidGlassSidebar<Content: View>: View {
    let content: Content
    let intensity: LiquidGlassIntensity

    init(intensity: LiquidGlassIntensity = .medium, @ViewBuilder content: () -> Content) {
        self.intensity = intensity
        self.content = content()
    }

    var body: some View {
        content
            .background(
                Rectangle()
                    .fill(intensity.material)
                    .overlay(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(intensity.opacity * 0.2),
                                Color.clear,
                                Color.black.opacity(intensity.opacity * 0.05)
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
    }
}

struct MainWindowView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var logManager = LogManager.shared
    @State private var selectedSidebarItem: SidebarItem = .status

    var body: some View {
        NavigationSplitView {
            // Sidebar with Liquid Glass
            LiquidGlassSidebar(intensity: .medium) {
                SidebarView(selectedItem: $selectedSidebarItem)
            }
            .navigationSplitViewColumnWidth(min: 240, ideal: 260, max: 300)
        } detail: {
            // Detail View with Liquid Glass background
            DetailView(selectedItem: selectedSidebarItem)
                .navigationSplitViewColumnWidth(min: 500, ideal: 700, max: 1000)
                .background(
                    LiquidGlassBackground(
                        colors: [
                            Color.blue.opacity(0.08),
                            Color.purple.opacity(0.04),
                            Color.clear
                        ],
                        intensity: .subtle
                    )
                )
        }
        .navigationTitle("NtfyClip")
        .frame(minWidth: 900, minHeight: 650)
        .background(
            LiquidGlassBackground(
                colors: [
                    Color.blue.opacity(0.05),
                    Color.purple.opacity(0.03),
                    Color.clear
                ],
                intensity: .subtle
            )
        )
    }
}

// MARK: - Sidebar Items
enum SidebarItem: String, CaseIterable, Identifiable {
    case status = "Status"
    case settings = "Settings"
    case logs = "Logs"

    var id: String { rawValue }

    var icon: String {
        switch self {
        case .status:
            return "chart.line.uptrend.xyaxis"
        case .settings:
            return "gearshape"
        case .logs:
            return "doc.text"
        }
    }
}

// MARK: - Sidebar View
struct SidebarView: View {
    @Binding var selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        VStack(spacing: 0) {
            // Header with app info - Enhanced with Liquid Glass
            LiquidGlassToolbar(intensity: .medium) {
                VStack(spacing: 10) {
                    Image(systemName: "doc.on.clipboard")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundStyle(.blue.gradient)

                    Text("NtfyClip")
                        .font(.title2)
                        .fontWeight(.semibold)

                    Text("Clipboard Sync")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Navigation List with improved spacing and Liquid Glass background
            List(SidebarItem.allCases, selection: $selectedItem) { item in
                SidebarItemView(item: item, isSelected: selectedItem == item)
                    .tag(item)
                    .listRowSeparator(.hidden)
                    .listRowBackground(Color.clear)
                    .padding(.vertical, 6) // Increased spacing between items for better touch targets
            }
            .listStyle(.sidebar)
            .scrollContentBackground(.hidden)
            .padding(.horizontal, 12)
            .padding(.vertical, 16)
            .background(
                LiquidGlassEffect(intensity: .subtle)
                    .opacity(0.5)
            )

            // Footer with sync control - Enhanced with Liquid Glass
            LiquidGlassToolbar(intensity: .medium) {
                VStack(spacing: 16) {
                    Divider()
                        .opacity(0.3)

                    LiquidGlassButton(intensity: .medium, style: .prominent, action: {
                        appViewModel.toggleSync()
                    }) {
                        HStack(spacing: 12) {
                            Image(systemName: appViewModel.isSyncEnabled ? "stop.circle.fill" : "play.circle.fill")
                                .foregroundColor(appViewModel.isSyncEnabled ? .red : .green)
                                .font(.system(size: 16, weight: .medium))

                            Text(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
                                .fontWeight(.medium)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .help(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
                }
            }
        }
        .navigationTitle("NtfyClip")
    }
}

// MARK: - Sidebar Item View
struct SidebarItemView: View {
    let item: SidebarItem
    let isSelected: Bool
    @State private var isHovered = false

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: item.icon)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(isSelected ? .white : (isHovered ? .accentColor : .primary))
                .frame(width: 24, height: 24)

            Text(item.rawValue)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(isSelected ? .white : .primary)

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12) // Increased touch target
        .background(
            Group {
                if isSelected {
                    LiquidGlassCard(intensity: .strong, cornerRadius: 10) {
                        Rectangle()
                            .fill(Color.accentColor.opacity(0.8))
                    }
                } else if isHovered {
                    LiquidGlassCard(intensity: .subtle, cornerRadius: 10) {
                        Rectangle()
                            .fill(Color.accentColor.opacity(0.1))
                    }
                } else {
                    Color.clear
                }
            }
        )
        .contentShape(Rectangle())
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Detail View
struct DetailView: View {
    let selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        Group {
            switch selectedItem {
            case .status:
                StatusDetailView()
            case .settings:
                SettingsDetailView()
            case .logs:
                LogsDetailView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.windowBackgroundColor))
    }
}


